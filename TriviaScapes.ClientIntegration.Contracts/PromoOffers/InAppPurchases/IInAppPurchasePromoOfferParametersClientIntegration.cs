using Xm.Billing.ClientIntegration.Contracts.Prices;
using Xm.Contracts.Abstractions;
using Xm.PromoOffers.ClientIntegration.Contracts;

namespace TriviaScapes.ClientIntegration.Contracts.PromoOffers.InAppPurchases
{
    [SerializableContract("ciiapppoffp")]
    public interface IInAppPurchasePromoOfferParametersClientIntegration : IXmPromoOfferParametersClientIntegration
    {
        [SerializableContractProperty("pt", 0)]
        IXmInAppProductPriceTagClientIntegration PriceTag { get; }

        [SerializableContractProperty("cs", 1)]
        IInAppPurchasePromoOfferColorSetClientIntegration ColorSet { get; }
    }
}