using System;
using System.Collections.Generic;
using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Ads
{
    [SerializableContract("ciadp")]
    public interface IAdDisplayProfileClientIntegration
    {
        [SerializableContractProperty("ir", 0)]
        IReadOnlyList<IInterstitialAdDisplayConditionClientIntegration> InterstitialConditions { get; }

        [SerializableContractProperty("dir", 1)]
        IInterstitialAdDisplayRuleClientIntegration DefaultInterstitialRule { get; }

        [SerializableContractProperty("drewpre", 2)]
        [Obsolete("Since " + nameof(AppReleaseVersions.RemoveAdPreloadingConditions))]
        bool DelayedRewardedPreloadingEnabled { get; }

        [SerializableContractProperty("ba", 3)]
        IBannerAdsClientIntegration BannerAds { get; }
    }
}