using Xm.Contracts.Abstractions;

namespace TriviaScapes.ClientIntegration.Contracts.Rewards.Levels
{
    [SerializableContract("cilvlrew")]
    [SerializableContractFormat(SerializableContractFormat.Json)]
    [SerializableContractFormat(SerializableContractFormat.BinaryJson)]
    [SerializableContractFormat(SerializableContractFormat.MessagePack)]
    public interface ILevelRewardClientIntegration
    {
        [SerializableContractProperty("addc", 0)]
        int AdditionalCoins { get; }

        [SerializableContractProperty("multset", 1)]
        ILevelRewardMultiplicationSettingsClientIntegration MultiplicationSettings { get; }
    }
}