using System.Collections.Generic;
using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts.Ads;

namespace TriviaScapes.Endpoint.Ads.DisplayProfiles
{
    internal class AdDisplayProfileSettings : IInterstitialAdDisplayRuleClientIntegration, IAdDisplayProfileClientIntegration
    {
        public AdDisplayProfileSettings()
        {
            DefaultInterstitialRulePeriod = InterstitialAdDisplayConditionSettings.DefaultPeriod;
        }

        [ConfigUnique]
        [ConfigCollectionKey]
        [ConfigCollectionDisplayKey]
        [ConfigDisplayStyle(IsHeader = true)]
        public string Name { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public InterstitialAdDisplayConditionSettings[] InterstitialConditions { get; private set; }

        [ConfigDisplayStyle(IsHeader = true)]
        public InterstitialAdDisplayRuleType DefaultInterstitialRule { get; private set; }

        [DefaultValue(InterstitialAdDisplayConditionSettings.DefaultPeriod)]
        [ConfigDisplayStyle(IsHeader = true)]
        public int DefaultInterstitialRulePeriod { get; private set; }

        [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
        public BannerAdsSettings BannerAds { get; private set; }

        IReadOnlyList<IInterstitialAdDisplayConditionClientIntegration> IAdDisplayProfileClientIntegration.InterstitialConditions => InterstitialConditions;

        IInterstitialAdDisplayRuleClientIntegration IAdDisplayProfileClientIntegration.DefaultInterstitialRule => this;

        IBannerAdsClientIntegration IAdDisplayProfileClientIntegration.BannerAds => BannerAds?.Enabled == true ? BannerAds : null;

        InterstitialAdDisplayRuleType IInterstitialAdDisplayRuleClientIntegration.Type => DefaultInterstitialRule;

        int IInterstitialAdDisplayRuleClientIntegration.Period => DefaultInterstitialRulePeriod;

        int IInterstitialAdDisplayRuleClientIntegration.PreloadingPeriod => 0;

        bool IAdDisplayProfileClientIntegration.DelayedRewardedPreloadingEnabled => false;
    }
}