using System.Collections;
using System.Collections.Generic;
using TriviaScapes.ClientIntegration.Contracts.GameActivities;
using Xm.GameActivities.Presets.Scenarios;

namespace TriviaScapes.Endpoint.GameActivities.PromoOffers;

public class PromoOfferActivitySettings : IEnumerable<XmGameActivityScenarioReference>
{
    [GameActivityScenario(GameActivitySlot.PeriodicOffers1)]
    public string Periodic1Scenario { get; internal set; }

    [GameActivityScenario(GameActivitySlot.PeriodicOffers2)]
    public string Periodic2Scenario { get; internal set; }

    public IEnumerator<XmGameActivityScenarioReference> GetEnumerator()
    {
        yield return GameActivitySlot.PeriodicOffers1.GetScenarioReference(Periodic1Scenario);
        yield return GameActivitySlot.PeriodicOffers2.GetScenarioReference(Periodic2Scenario);
    }

    IEnumerator IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }
}