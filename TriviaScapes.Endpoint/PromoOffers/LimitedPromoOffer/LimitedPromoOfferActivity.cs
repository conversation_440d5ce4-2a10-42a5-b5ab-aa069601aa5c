using TriviaScapes.ClientIntegration.Contracts.PromoOffers.LimitedPromoOffer;
using Xm.GameActivities;
using Xm.PromoOffers.GameActivities.ClientIntegration.Contracts;

namespace TriviaScapes.Endpoint.PromoOffers.LimitedPromoOffer
{
    internal class LimitedPromoOfferActivity : XmGameActivity<ILimitedPromoOfferActivityParametersClientIntegration>
    {
        private readonly string _offerName;
        private readonly int _priority;

        public LimitedPromoOfferActivity(string offerName, int priority)
        {
            _offerName = offerName;
            _priority = priority;
        }

        public override string GetDescription()
        {
            return $"Promo Offer '{_offerName}' with {_priority} priority";
        }

        protected override ILimitedPromoOfferActivityParametersClientIntegration GetParameters(XmGameActivityParameterContext context)
        {
            return new XmPromoOfferActivityParametersClientIntegrationDto()
            {
                OfferName = _offerName,
                Priority = _priority
            };
        }
    }
}
