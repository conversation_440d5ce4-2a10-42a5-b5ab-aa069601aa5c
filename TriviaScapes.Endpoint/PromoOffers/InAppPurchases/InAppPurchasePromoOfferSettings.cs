using System.ComponentModel;
using Lr.Basic.Config.Attributes;
using Lr.Basic.Config.Data;
using TriviaScapes.ClientIntegration.Contracts.PromoOffers.InAppPurchases;
using TriviaScapes.Endpoint.PromoOffers.ColorSets.Extensions;
using Xm.Billing.ClientIntegration.Contracts.Prices;
using Xm.Billing.Prices;
using Xm.PromoOffers;
using Xm.PromoOffers.Config;

namespace TriviaScapes.Endpoint.PromoOffers.InAppPurchases;

[ConfigInherit]
[DisplayName("In App Purchase")]
internal class InAppPurchasePromoOfferSettings : XmPromoOfferSettings<IInAppPurchasePromoOfferParametersClientIntegration>
{
    [ConfigDisplayStyle(DisplayStyle = DisplayStyle.Inline)]
    public XmAnyInAppProductPriceTagSettings PriceTag { get; internal set; }

    [InAppPurchasePromoOfferColorSet]
    public string ColorSet { get; internal set; }

    protected override string GetDescription()
    {
        return $"{PriceTag?.Description} ({ColorSet})";
    }

    protected override IInAppPurchasePromoOfferParametersClientIntegration GetParameters(XmPromoOfferParameterContext context)
    {
        var priceTag = PriceTag?.ToClientIntegration(context.SerializableContractRepository);
        var colorSet = context.Extensions.Get<PromoOfferColorSetExtension>().ColorSetRepository.GetInAppPurchaseColorSet(ColorSet);

        return priceTag == null
            ? null
            : new InAppPurchasePromoOfferParametersClientIntegrationDto()
            {
                PriceTag = new XmInAppProductPriceTagClientIntegrationDto(priceTag),
                ColorSet = colorSet == null
                    ? null
                    : new InAppPurchasePromoOfferColorSetClientIntegrationDto(colorSet),
            };
    }
}