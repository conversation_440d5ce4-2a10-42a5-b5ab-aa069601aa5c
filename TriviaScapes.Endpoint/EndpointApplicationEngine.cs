using System.Collections.Generic;
using Autofac;
using TriviaScapes.ClientIntegration.Contracts.GameActivities;
using TriviaScapes.ClientIntegration.Contracts.PromoOffers.Views;
using TriviaScapes.ClientIntegration.Contracts.PromoOffers.Views.Placements;
using TriviaScapes.Dto;
using TriviaScapes.Endpoint.Achievements;
using TriviaScapes.Endpoint.Ads;
using TriviaScapes.Endpoint.Billing;
using TriviaScapes.Endpoint.Campaign;
using TriviaScapes.Endpoint.Campaign.Designs;
using TriviaScapes.Endpoint.HapticFeedback;
using TriviaScapes.Endpoint.ImageCollections;
using TriviaScapes.Endpoint.Leaderboards;
using TriviaScapes.Endpoint.Misc;
using TriviaScapes.Endpoint.Onboarding;
using TriviaScapes.Endpoint.Presets;
using Xm.Adjust.Config;
using Xm.Adjust.Integration;
using Xm.Advertisement.Billing.Integration;
using Xm.Advertisement.Mediations.Amazon;
using Xm.Aerospike.Engine;
using Xm.ApplicationEngine.Config;
using Xm.ApplicationEngine.Engines;
using Xm.Billing.UserRewards;
using Xm.Commands;
using Xm.Contracts.Schemas.Integration;
using Xm.EventBus.RoutingModule;
using Xm.ExperimentalFeatures.Registration;
using Xm.GameActivities.Registration;
using Xm.Gameplays.Registration;
using Xm.Logging.EventBus;
using Xm.Presets;
using Xm.PromoOffers.Registration;
using Xm.QuestionDelivery.LevelMaps;
using Xm.UserAchievements;
using Xm.UserRewards;
using Xm.Utils;
using Xm.Utils.Runtime;

namespace TriviaScapes.Endpoint
{
    public class EndpointApplicationEngine : DefaultApplicationEngine
    {
        protected override IEnumerable<string> SetupConfigStorageLayerInitializationOrder()
        {
            yield return ConfigLayers.Gameplay;
            yield return XmPresetConstants.ConfigLayer;
            yield return ConfigLayers.Application;
        }

        protected override void RegisterModules(ContainerBuilder modulesBuilder)
        {
            base.RegisterModules(modulesBuilder);

            RegisterModule<SerializableContractSchemaIntegrationEngineModule>(modulesBuilder);
            RegisterModule(modulesBuilder, _ => new XmEventBusRoutingModule());
            RegisterModule(modulesBuilder, _ => new XmLoggingEventBusEngineModule());
            RegisterModule(modulesBuilder, _ => new PresetEngineModule(PresetSerializableContractRepository.Instance));

            RegisterModule<XmAdjustConfigModule>(modulesBuilder);
            RegisterModule<XmAdjustIntegrationModule>(modulesBuilder);

            RegisterModule(modulesBuilder, _ => new AerospikeServiceModule());
            RegisterModule(modulesBuilder, _ => new AerospikeConfigurationModule(ApplicationRuntimeInfo.Current.ProjectGroup.FirstUpper()));
            RegisterModule(modulesBuilder, _ => new XmCommandModule<CommandContext>(SerializableContractRepository.Instance));
            RegisterModule(modulesBuilder, _ => new ApiGatewayIntegrationModule(SerializableContractRepository.Instance));
            RegisterModule(modulesBuilder, _ => new AdEngineModule(SerializableContractRepository.Instance));
            RegisterModule<XmAdvertisementBillingIntegrationEngineModule>(modulesBuilder);
            RegisterModule(modulesBuilder, _ => new BillingEngineModule(SerializableContractRepository.Instance));
            RegisterModule(modulesBuilder, _ => new LeaderboardEngineModule(SerializableContractRepository.Instance));
            RegisterModule(modulesBuilder, _ => new AchievementEngineModule(SerializableContractRepository.Instance));

            RegisterModule<XmQuestionDeliveryLevelMapEngineModule>(modulesBuilder);

            RegisterModule(modulesBuilder, _ => new CampaignLevelDesignEngineModule(SerializableContractRepository.Instance));
            RegisterModule(modulesBuilder, _ => new MiscModule(SerializableContractRepository.Instance));
            RegisterModule(modulesBuilder, _ => new XmUserRewardEngineModule(SerializableContractRepository.Instance));
            RegisterModule(modulesBuilder, _ => new XmUserAchievementEngineModule(SerializableContractRepository.Instance));
            RegisterModule(modulesBuilder, _ => new ImageCollectionEngineModule(SerializableContractRepository.Instance));
            RegisterModule(modulesBuilder, _ => new XmBillingUserRewardEngineModule());

            RegisterModule(modulesBuilder, _ => new OnboardingEngineModule(SerializableContractRepository.Instance));
            RegisterModule(modulesBuilder, _ => new HapticFeedbackEngineModule(SerializableContractRepository.Instance));

            RegisterModule<XmExperimentalFeatureEngineModule>(modulesBuilder);
            RegisterModule<XmAmazonAdvertisementMediationEngineModule>(modulesBuilder);

            RegisterModule(modulesBuilder,
              _ => new XmGameActivityEngineModule(
                  SerializableContractRepository.Instance,
                  b => b.AddSlots("Events", GameActivitySlot.Lobby1, GameActivitySlot.Lobby2, GameActivitySlot.Lobby3)
                        .AddSlot(GameActivitySlot.ImageCollection)
                        .AddSlot(GameActivitySlot.BrainScore)
                        .AddSlots("Offers", GameActivitySlot.PeriodicOffers1, GameActivitySlot.PeriodicOffers2)
                        .AddAdvertisement()
                        .AddPromoOffers()
              )
            );

            RegisterModule(modulesBuilder,
              _ => new XmGameplayEngineModule(
                  SerializableContractRepository.Instance
              )
            );

            RegisterModule(modulesBuilder,
              _ => new CampaignEngineModule(
                  SerializableContractRepository.Instance
              )
            );

            RegisterModule(modulesBuilder,
              _ => new XmPromoOfferEngineModule(
                  SerializableContractRepository.Instance,
                  b => b.AddFullScreenViewType<PromoOfferFullScreenPlacement>()
                        .AddViewType<PromoOfferBannerPlacement>(PromoOfferViewTypes.Banner)
                        .AddViewType<PromoOfferIconPlacement>(PromoOfferViewTypes.Icon)
                        .AddColorSets()
              )
            );
        }
    }
}